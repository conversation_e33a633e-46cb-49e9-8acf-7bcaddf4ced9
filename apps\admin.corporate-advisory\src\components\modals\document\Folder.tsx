import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { Modal, Stack, TextInput, Select, NumberInput } from "@mantine/core";
import { useEffect, useMemo } from "react";
import { Submit<PERSON><PERSON><PERSON>, useForm, Controller } from "react-hook-form";
import api from "@/apis";
import noty from "@code.8cent/react/noty";
import useModalStore from "@/store/modal";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import ModalFooter from "@/components/common/ModalFooter";
import { Check, X } from "@phosphor-icons/react";
import { useMemoizedFn } from "ahooks";

const folderSchema = z.object({
    foldersNameEN: z.string().min(1, "请输入文件夹名称(英文)"),
    foldersNameZH: z.string().min(1, "请输入文件夹名称(中文)"),
    foldersNameZT: z.string().min(1, "请输入文件夹名称(繁体)"),
    foldersNameMS: z.string().min(1, "请输入文件夹名称(马来)"),
    parentId: z.number().default(0),
    order: z.number().default(0),
});

type FolderForm = z.infer<typeof folderSchema>;

const useFolderForm = (folder: any) => {
    const {
        handleSubmit,
        formState: { errors },
        setValue,
        reset,
        control,
    } = useForm<FolderForm>({
        defaultValues: {
            foldersNameEN: folder?.foldersNameEN || "",
            foldersNameZH: folder?.foldersNameZH || "",
            foldersNameZT: folder?.foldersNameZT || "",
            foldersNameMS: folder?.foldersNameMS || "",
            parentId: folder?.parentId || 0,
            order: folder?.order || 0,
        },
        resolver: zodResolver(folderSchema),
    });

    useEffect(() => {
        if (folder) {
            setValue("foldersNameEN", folder.foldersNameEN);
            setValue("foldersNameZH", folder.foldersNameZH);
            setValue("foldersNameZT", folder.foldersNameZT);
            setValue("foldersNameMS", folder.foldersNameMS);
            setValue("parentId", folder.parentId);
        }
    }, [folder, setValue]);

    return {
        handleSubmit,
        errors,
        control,
        setValue,
        reset
    };
};

interface FolderProps {
    onUpdateSuccess?: () => void;
}

const Folder = ({ onUpdateSuccess = () => {} }: FolderProps) => {
    const { lang } = useSettingStore();
    const openConfirm = useModalStore.use.openConfirm();
    const { isVisible, close, modalParams } = useModalStore((state) => ({
        isVisible: state.documentFolderModal,
        close: state.close,
        modalParams: state.modalParams.documentFolderModal,
    }));

    const folders = modalParams?.folders;
    const folder = modalParams?.folder;

    const { handleSubmit, errors, control, setValue, reset } = useFolderForm(folder);

    const closeModal = useMemoizedFn(() => {
        // 清空表单数据
        setValue("foldersNameEN", "");
        setValue("foldersNameZH", "");
        setValue("foldersNameZT", "");
        setValue("foldersNameMS", "");
        setValue("parentId", 0);
        setValue("order", 0);
        reset();
        close("documentFolderModal");
    });

    const submitForm: SubmitHandler<FolderForm> = useMemoizedFn(async (data) => {
        try {
            const payload: TDocumentFolderStoreParams = {
                parentId: data.parentId || 0,
                order: data.order || 0,
                foldersNameEN: data.foldersNameEN,
                foldersNameZH: data.foldersNameZH,
                foldersNameZT: data.foldersNameZT,
                foldersNameMS: data.foldersNameMS,
            };

            const res = folder?.id
                ? await api.document.folders.update(folder.id, payload)
                : await api.document.folders.store(payload);

            if (res) {
                noty.success("操作成功");
                onUpdateSuccess();
                closeModal();
            } else {
                noty.error("操作失败");
            }
        } catch (error) {
            console.error("提交表单失败:", error);
            noty.error("操作失败");
        }
    });

    const handleSave = useMemoizedFn(() => {
        openConfirm({
            title: "提示",
            message: "您确定更新文件夹的信息么？",
            onConfirm: handleSubmit(submitForm),
        });
    });

    const folderOptions = useMemo(() => {
        return (
            folders?.map((item) => ({
                value: item.id.toString(),
                label: item[`foldersName${lang}`],
            })) || []
        );
    }, [folders, lang]);

    return (
        <Modal
            opened={isVisible}
            onClose={closeModal}
            title="创建文件夹"
            size="xl"
        >
            <form onSubmit={handleSubmit(handleSave)}>
                <Stack gap="lg">
                    <Controller
                        control={control}
                        name="parentId"
                        render={({ field }) => (
                            <Select
                                labelProps={{ className: "profile-form-label" }}
                                label="请选择上级文件夹"
                                placeholder="请选择上级文件夹"
                                data={folderOptions}
                                onChange={(value) => field.onChange(value ? Number(value) : 0)}
                                value={field.value ? String(field.value) : null}
                            />
                        )}
                    />

                    {["EN", "ZH", "ZT", "MS"].map((lang) => {
                        // 检查原始中文名是否为受保护的名称
                        const isProtectedOriginal = lang === "ZH" && (folder?.foldersNameZH === "团队文件" || folder?.foldersNameZH === "招募工具");
                        // 当前语言是中文且原始名称不是受保护名称时，需要检查输入
                        const shouldCheckInput = lang === "ZH" && !isProtectedOriginal;

                        return (
                            <Controller
                                key={lang}
                                control={control}
                                name={`foldersName${lang}` as keyof FolderForm}
                                render={({ field }) => (
                                    <TextInput
                                        labelProps={{ className: "profile-form-label" }}
                                        label={`文件夹名称(${
                                            lang === "EN"
                                                ? "英文"
                                                : lang === "ZH"
                                                ? "中文"
                                                : lang === "ZT"
                                                ? "繁体"
                                                : "马来"
                                        })`}
                                        placeholder={`输入文件夹名称(${
                                            lang === "EN"
                                                ? "英文"
                                                : lang === "ZH"
                                                ? "中文"
                                                : lang === "ZT"
                                                ? "繁体"
                                                : "马来"
                                        })`}
                                        {...field}
                                        // disabled={isProtectedOriginal}
                                        onChange={(e) => {
                                            const value = e.target.value;
                                            // if (shouldCheckInput && (value === "团队文件" || value === "招募工具")) {
                                            //     // 重置输入框
                                            //     field.onChange("");
                                            //     // 显示提示
                                            //     noty.error(`无法编辑"${value}"`);
                                            // } else {
                                                field.onChange(value);
                                            // }
                                        }}
                                        error={errors[`foldersName${lang}`]?.message}
                                    />
                                )}
                            />
                        );
                    })}

                    <Controller
                        control={control}
                        name="order"
                        render={({ field: { onChange, value, ...field } }) => (
                            <NumberInput
                                labelProps={{ className: "profile-form-label" }}
                                label="排序"
                                placeholder="输入排序"
                                {...field}
                                value={value || 0}
                                onChange={(val) => onChange(val === "" ? 0 : Number(val))}
                                error={errors.order?.message}
                            />
                        )}
                    />

                    <ModalFooter
                        buttons={[
                            {
                                key: "save",
                                label: "保存",
                                style: "outline",
                                type: "submit",
                                leftSection: <Check size={16} />,
                            },
                            {
                                key: "cancel",
                                label: "取消",
                                leftSection: <X size={16} />,
                                onClick: closeModal,
                            },
                        ]}
                    />
                </Stack>
            </form>
        </Modal>
    );
};

export default Folder;
