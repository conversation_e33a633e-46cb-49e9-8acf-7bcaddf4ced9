import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import {
    Group,
    Modal,
    Stack,
    TextInput,
    Textarea,
    Text,
    Select,
    Input,
    Tooltip,
    Divider,
    Radio,
    NumberInput,
} from "@mantine/core";
import { useEffect, useState } from "react";
import { Submit<PERSON><PERSON><PERSON>, useForm, Controller } from "react-hook-form";
import api from "@/apis";
import noty from "@code.8cent/react/noty";
import useModalStore from "@/store/modal";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Dropzone, MIME_TYPES } from "@mantine/dropzone";
import { FileText, Eye, DownloadSimple, CalendarBlank, Check, X } from "@phosphor-icons/react";
import { DateTimePicker } from "@mantine/dates";
import dayjs from "dayjs";
import { useMemoizedFn, useRequest } from "ahooks";
import ModalFooter from "@/components/common/ModalFooter";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import { cnaRequest } from "@code.8cent/utils";
import { useShallow } from "zustand/react/shallow";

const documentSchema = z
    .object({
        documentTitle: z.string().min(1, "请输入文档名称"),
        folderId: z.number().min(1, "请选择文件夹"),
        order: z.number().default(0),
        documentVersion: z.string().min(1, "请输入文档版本"),
        documentDescription: z
            .string()
            .optional()
            .refine(
                (value) => value === undefined || value.length <= 100,
                "请输入少于100个字符的文档描述"
            ),
        documentLanguage: z.string().min(1, "请选择语言"),
        documentValidity: z.string().min(1, "请选择有效期"),
        documentFile: z
            .any()
            .optional()
            .refine((file) => file.length == 0 || file instanceof File, "请选择上传文件")
            .refine(
                (file) =>
                    file.length == 0 ||
                    (file.size <= 1024 * 1024 * 50 &&
                        [MIME_TYPES.pdf, MIME_TYPES.doc, MIME_TYPES.docx].includes(file.type)),
                "文件必须是PDF、Word文档，且大小不能超过50MB"
            ),
        documentVersionID: z
            .number()
            .optional()
            .refine((v) => v === undefined || v === 0 || typeof v === "number", "请选择版本"),
        type: z.number().optional(),
    })
    .refine(
        (data) => {
            // If a version is selected, file is not required
            if (data.documentVersionID && data.documentVersionID !== 0) {
                return true;
            }
            // If no version is selected, file is required
            return data.documentFile.length != 0;
        },
        {
            message: "请选择文件或提供有效的版本ID",
            path: ["documentFile"],
        }
    );

type DocumentForm = z.infer<typeof documentSchema> & {
    documentID: number;
};

const Create = ({ onUpdateSuccess = () => {} }: { onUpdateSuccess?: () => void }) => {
    const { lang } = useSettingStore();
    const [loading, setLoading] = useState(false);
    const [files, setFiles] = useState<File[]>([]);

    const openConfirm = useModalStore.use.openConfirm();
    const { openFileView } = useFileViewer();

    const modalParams = useModalStore((state) => state.modalParams.documentCreateModal);
    const folders = modalParams?.folders;
    const document = modalParams?.document;

    const { isVisible, close } = useModalStore(
        useShallow((state) => ({
            isVisible: state.documentCreateModal,
            close: state.close,
        }))
    );

    const closeModal = useMemoizedFn(() => {
        // 清空表单和重置抱错
        reset();
        clearErrors();
        setFiles([]);
        close("documentCreateModal");
    });

    const {
        register,
        handleSubmit,
        reset,
        clearErrors,
        formState: { errors },
        setValue,
        control,
    } = useForm<DocumentForm>({
        defaultValues: {
            documentTitle: document?.documentTitle || "",
            documentDescription: document?.documentDescription || "",
            documentLanguage: document?.documentLanguage || "",
            documentValidity: document?.documentValidity && dayjs(document.documentValidity).isValid()
                ? dayjs(document.documentValidity).format("YYYY-MM-DD HH:mm:ss")
                : "",
            documentVersionID:
                document?.allFiles?.length > 0
                    ? document?.allFiles?.find((v) => v.isShow)?.documentXID ||
                      document?.allFiles?.[0]?.documentXID
                    : 0,
            folderId: document?.folderId || 0,
            order: document?.order || 0,
            documentVersion: document?.documentVersion || "",
            type: document?.type?.toString() || "1",
        },
        resolver: zodResolver(documentSchema),
    });

    useEffect(() => {
        if (document) {
            setValue("documentTitle", document.documentTitle);
            setValue("documentDescription", document.documentDescription);
            setValue("documentLanguage", document.documentLanguage);
            setValue(
                "documentValidity",
                document.documentValidity && dayjs(document.documentValidity).isValid()
                    ? dayjs(document.documentValidity).format("YYYY-MM-DD HH:mm:ss")
                    : ""
            );
            setValue(
                "documentVersionID",
                document?.allFiles?.length > 0
                    ? document?.allFiles?.find((v) => v.isShow)?.documentXID ||
                          document?.allFiles?.[0]?.documentXID
                    : 0
            );
            setValue("folderId", document.folderId);
            setValue("order", document.order);
            setValue("documentVersion", document.documentVersion);
        }
    }, [document, setValue]);

    const submitForm: SubmitHandler<DocumentForm> = useMemoizedFn(async (data) => {
        console.log("🚀 ~ Create.tsx:158 ~ Create ~ data:", data)
        setLoading(true);
        try {
            const payload: any = {
                documentTitle: data.documentTitle,
                documentDescription: data.documentDescription,
                documentLanguage: data.documentLanguage,
                documentValidity: data.documentValidity,
                folderId: data.folderId,
                order: data.order || 0,
                documentVersion: data.documentVersion,
                type: data.type,
            };

            if (data.documentFile.length != 0) {
                payload.documentFile = data.documentFile;
            } else {
                payload.versionid = data.documentVersionID;
            }

            let res: Boolean;
            if (document?.documentID) {
                res = await api.document.update(document.documentID, payload);
            } else {
                res = await api.document.store(payload);
            }

            if (res) {
                noty.success("操作成功");
                onUpdateSuccess();
                closeModal();
            }
        } catch (error) {
            noty.error("操作失败");
            console.error(error);
        } finally {
            setLoading(false);
        }
    });

    const handleSave = useMemoizedFn(() => {
        openConfirm({
            title: "提示",
            message: "您确定更新的文档信息么？",
            onConfirm: handleSubmit(submitForm),
        });
    });

    const actionText = document?.documentTitle ? "更新" : "创建";

    const { run: previewDocumentFile } = useRequest(
        async (id: number, label: string) => {
            let response = await cnaRequest<string>(
                `/api/v1/admin/documents/generateKey/${id}`,
                "GET",
                {
                    id,
                }
            );

            if (!response.error) {
                const fileToken = response.result.data;
                openFileView(`${window.api_base_url}/api/v1/admin/documents/preview/${fileToken}`, {
                    title: t(label, lang),
                });
            } else {
                noty.error(response.error.message);
            }
        },
        {
            manual: true,
        }
    );

    const { run: downloadDocumentFile } = useRequest(
        async (id: number) => {
            let response = await cnaRequest<string>(
                `/api/v1/admin/documents/generateKey/${id}`,
                "GET",
                {
                    id,
                }
            );

            if (!response.error) {
                const fileToken = response.result.data;
                window.open(
                    `${window.api_base_url}/api/v1/admin/documents/download/${fileToken}`,
                    "_blank"
                );
            } else {
                noty.error(response.error.message);
            }
        },
        {
            manual: true,
        }
    );

    return (
        <Modal
            opened={isVisible}
            onClose={closeModal}
            title={actionText + "文档资料"}
            size="xl"
        >
            <form onSubmit={handleSubmit(handleSave, (errors) => console.log(errors))}>
                <Stack gap={"lg"}>
                    <Controller
                        name="documentTitle"
                        control={control}
                        render={({ field }) => (
                            <TextInput
                                {...field}
                                label="文档名称"
                                placeholder="请输入文档名称"
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                error={errors.documentTitle?.message}
                            />
                        )}
                    />
                    <Controller
                        name="folderId"
                        control={control}
                        render={({ field }) => (
                            <Select
                                {...field}
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                label="所在文件夹"
                                placeholder="请选择所在文件夹"
                                data={folders?.map((folder) => ({
                                    value: folder.id.toString(),
                                    label: folder[`foldersName${lang}`],
                                }))}
                                onChange={(value) => field.onChange(value ? parseInt(value) : 0)}
                                value={field.value ? String(field.value) : null}
                                error={errors.folderId?.message}
                            />
                        )}
                    />
                    <Controller
                        control={control}
                        name="order"
                        render={({ field: { onChange, value, ...field } }) => (
                            <NumberInput
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                label="排序"
                                placeholder="输入排序"
                                {...field}
                                value={value || 0}
                                onChange={(val) => {
                                    onChange(val === "" ? 0 : Number(val));
                                }}
                                error={errors.order?.message}
                            />
                        )}
                    />
                    <Controller
                        control={control}
                        name="type"
                        render={({ field: { onChange, value, ...field } }) => (
                            <Select
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                label="是否显示"
                                placeholder="请选择是否显示"
                                data={[
                                    { value: "1", label: "显示" },
                                    { value: "0", label: "隐藏" },
                                ]}
                                {...field}
                                value={value?.toString() || "1"}
                                onChange={(val) => {
                                    onChange(val === "" ? "1" : val);
                                }}
                                error={errors.type?.message}
                            />
                        )}
                    />
                    <Controller
                        name="documentVersion"
                        control={control}
                        render={({ field }) => (
                            <TextInput
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                label="文档版本"
                                placeholder="请输入文档版本"
                                {...field}
                                error={errors.documentVersion?.message}
                            />
                        )}
                    />
                    <Controller
                        name="documentLanguage"
                        control={control}
                        render={({ field }) => (
                            <Select
                                {...field}
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                label="文档语言"
                                placeholder="请选择文档的语言"
                                data={[
                                    { value: "ZH", label: "中文" },
                                    { value: "ZT", label: "繁体中文" },
                                    { value: "EN", label: "English" },
                                    { value: "MS", label: "Malay" },
                                ]}
                                error={errors.documentLanguage?.message}
                            />
                        )}
                    />
                    <Controller
                        name="documentValidity"
                        control={control}
                        render={({ field }) => (
                            <DateTimePicker
                                {...field}
                                withSeconds
                                valueFormat="YYYY-MM-DD HH:mm:ss"
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                value={field.value && dayjs(field.value).isValid() ? dayjs(field.value).toDate() : null}
                                onChange={(value) => {
                                    field.onChange(value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : "");
                                }}
                                label="文档有效期"
                                placeholder="请选择文档的有效期"
                                leftSection={<CalendarBlank size={16} />}
                                error={errors.documentValidity?.message}
                            />
                        )}
                    />
                    <Textarea
                        labelProps={{
                            className: "profile-form-label",
                        }}
                        autosize
                        resize="vertical"
                        description={`输入少于 100 个的字符`}
                        label="文档描述"
                        placeholder="输入文档描述"
                        {...register("documentDescription")}
                        error={errors.documentDescription?.message}
                    />
                    <Input.Wrapper
                        labelProps={{
                            className: "profile-form-label",
                        }}
                        label="文档附件"
                        {...register("documentFile")}
                    >
                        <Dropzone
                            onDrop={(files: File[]) => {
                                const file = files[0];
                                // 限制文件大小
                                console.log("file.size", file.size);
                                if (file.size > 1024 * 1024 * 50) {
                                    noty.error("文件大小不能超过50MB");
                                    return;
                                }
                                setValue("documentFile", file);
                                setFiles(files);
                            }}
                            multiple={false}
                            accept={[MIME_TYPES.pdf, MIME_TYPES.doc, MIME_TYPES.docx]}
                        >
                            <Stack className="tw-flex tw-items-center">
                                <FileText
                                    size={36}
                                    className="tw-text-dimmed tw-mx-auto tw-mb-3"
                                />
                                <Text
                                    size="sm"
                                    c="dimmed"
                                >
                                    {files[0] ? files[0].name : t("upload.files", lang)}
                                </Text>
                            </Stack>
                        </Dropzone>
                        {errors?.documentFile && (
                            <Input.Error>
                                {typeof errors.documentFile.message === "string"
                                    ? errors.documentFile.message
                                    : String(errors.documentFile.message)}
                            </Input.Error>
                        )}
                    </Input.Wrapper>

                    {document?.allFiles?.length > 0 && (
                        <Stack gap="0">
                            <Controller
                                name="documentVersionID"
                                control={control}
                                render={({ field }) => (
                                    <Radio.Group
                                        {...field}
                                        value={field.value?.toString()}
                                        onChange={(value) => field.onChange(parseInt(value, 10))}
                                        label="文档版本"
                                        labelProps={{
                                            className: "profile-form-label",
                                        }}
                                    >
                                        {document?.allFiles.map((version, index) => (
                                            <Group
                                                key={index}
                                                className="tw-border tw-border-solid tw-border-gray-300 tw-rounded-md tw-p-2 tw-mb-2"
                                                justify="space-between"
                                                align="center"
                                            >
                                                <Radio
                                                    value={version.documentXID.toString()}
                                                    label={
                                                        version.documentVersion +
                                                        (version.isShow ? "（当前版本）" : "")
                                                    }
                                                />
                                                <Group>
                                                    <Tooltip label="查看">
                                                        <Eye
                                                            size={18}
                                                            className="tw-cursor-pointer"
                                                            onClick={() =>
                                                                previewDocumentFile(
                                                                    version.documentXID,
                                                                    "查看文档"
                                                                )
                                                            }
                                                        />
                                                    </Tooltip>
                                                    <Divider orientation="vertical" />
                                                    <Tooltip label="下载">
                                                        <DownloadSimple
                                                            size={18}
                                                            className="tw-cursor-pointer"
                                                            onClick={() =>
                                                                downloadDocumentFile(
                                                                    version.documentXID
                                                                )
                                                            }
                                                        />
                                                    </Tooltip>
                                                </Group>
                                            </Group>
                                        ))}
                                    </Radio.Group>
                                )}
                            />
                        </Stack>
                    )}

                    <ModalFooter
                        buttons={[
                            {
                                key: "submit",
                                label: actionText,
                                style: "outline",
                                type: "submit",
                                loading: loading,
                                leftSection: <Check size={16} />,
                            },
                            {
                                key: "cancel",
                                label: "取消",
                                leftSection: <X size={16} />,
                                onClick: closeModal,
                            },
                        ]}
                    />
                </Stack>
            </form>
        </Modal>
    );
};

export default Create;
